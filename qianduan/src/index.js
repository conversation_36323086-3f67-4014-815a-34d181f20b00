// React 由 JSX 转换自动处理
import React from 'react';
import ReactDOM from 'react-dom/client';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import ReactPlugin from '@stagewise-plugins/react';
import { useRequest } from 'ahooks';
import init, { chushihuawangguan, get_qingqiu } from './qudong/wangluoqingqiu/wangluoqingqiu.js';
import { wangguanpeizhi } from './peizhi/wangguanpeizhi.js';
import {
  tiqu_wangzhan_jichuxinxi,
  cunchu_dao_huancun,
  cong_huancun_duqu
} from './gongju/wangzhanjichuxinxi_huancun.js';
import {
  yingyong_wangzhan_jichuxinxi,
  kuaisu_yingyong_mingcheng_tubiao
} from './gongju/wangzhan_dongtai_yingyong.js';
// 导入路由重定向功能
import { chushihua_luyou_chongdingxiang } from './gongju/luyou_chongdingxiang.js';
// 导入明暗布局组件
import {
  Minganbuju,
} from './zujian/minganbuju';
// 导入顶部导航栏组件
import { Dingbudaohanglan } from './zujian/dingbudaohanglan';
// 导入路由检测Hook
import { useShifougengluyou } from './zujian/dingbudaohanglan/useLuyoujiance.js';
// 导入预加载动画组件
import Yujiazaizujian, { useYujiazai } from './zujian/yujiazaizujian';
// 导入路由检测Hook
import { useLuyoujiance } from './zujian/dingbudaohanglan/useLuyoujiance.js';
// 导入首页组件
import Shouye from './yemian/shouye';

const yuanshiconsole = console.log;
console.log = function(...args) {
  const message = args.join(' ');
  if (message.includes('[AgentProvider]') ||
      message.includes('[PanelsProvider]') ||
      message.includes('[PanelWrapper]') ||
      message.includes('[stagewise]')) {
    return;
  }
  yuanshiconsole.apply(console, args);
};

let chushipromise = null;
let yichushihua = false;
let wasm_yichushihua = false;

async function chushihua_wasm() {
    if (wasm_yichushihua) {
        return;
    }
    await init();
    chushihuawangguan(wangguanpeizhi.jichudizhi);
    wasm_yichushihua = true;
}

async function qianduanchushihua() {
  if (yichushihua) {
    return null;
  }
  if (chushipromise) {
    return await chushipromise;
  }

  chushipromise = (async () => {
    try {
      await chushihua_wasm();

      // 初始化路由重定向功能
      console.log('🔄 [初始化] 启用路由重定向功能');
      chushihua_luyou_chongdingxiang();

      let wangzhan_xinxi = cong_huancun_duqu();

      if (wangzhan_xinxi) {
        console.log('🎯 [初始化] 从缓存加载网站基础信息:', wangzhan_xinxi);
        kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi);
        yingyong_wangzhan_jichuxinxi(wangzhan_xinxi);
        yichushihua = true;
        return {
          laiyuan: 'huancun',
          shuju: wangzhan_xinxi
        };
      }

      const xiangying = await get_qingqiu(
        '/jiekou/wangzhanjichuxinxi',
        null,
        false,
        false,
        5000,
        1
      );

      wangzhan_xinxi = tiqu_wangzhan_jichuxinxi(xiangying);
      if (!wangzhan_xinxi) {
        yichushihua = true;
        throw new Error('提取网站基础信息失败');
      }

      console.log('🎯 [初始化] 从网络加载网站基础信息:', wangzhan_xinxi);
      kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi);
      cunchu_dao_huancun(wangzhan_xinxi);
      const yingyong_jieguo = yingyong_wangzhan_jichuxinxi(wangzhan_xinxi);

      yichushihua = true;
      return {
        laiyuan: 'wangluo',
        shuju: wangzhan_xinxi,
        yingyong_jieguo: yingyong_jieguo
      };

    } catch (cuowu) {
      console.error('前端初始化失败:', cuowu);
      chushipromise = null;
      throw cuowu;
    }
  })();

  return await chushipromise;
}

// 主题和导航栏监测器组件
function ZhutiheDaohanglanJianceqi({ onZhutiZhunbei, onDaohanglanZhunbei, caidanxiangmu, wangzhan_jichuxinxi, chuli_yemian_zujian_chushihua }) {
  const zhuti_zhunbei_ref = React.useRef(false);
  const daohanglan_zhunbei_ref = React.useRef(false);

  // 检测是否为根路由（首页）
  const shifougengluyou = useShifougengluyou();

  // 监听主题系统准备状态
  React.useEffect(() => {
    if (!zhuti_zhunbei_ref.current) {
      // 主题系统在Minganbuju组件渲染后就准备好了
      const timer = setTimeout(() => {
        zhuti_zhunbei_ref.current = true;
        onZhutiZhunbei();
      }, 100); // 给主题系统一点时间初始化

      return () => clearTimeout(timer);
    }
  }, [onZhutiZhunbei]);

  // 监听导航栏准备状态
  React.useEffect(() => {
    if (!daohanglan_zhunbei_ref.current) {
      // 导航栏组件渲染后就准备好了
      const timer = setTimeout(() => {
        daohanglan_zhunbei_ref.current = true;
        onDaohanglanZhunbei();

      }, 200); // 给导航栏组件一点时间渲染

      return () => clearTimeout(timer);
    }
  }, [onDaohanglanZhunbei]);

  return (
    <>
      <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />

      {/* 顶部导航栏 - 在首页和数据页面显示 */}
      <Dingbudaohanglan
        wangzhanmingcheng={wangzhan_jichuxinxi.wangzhan_mingcheng}
        wangzhanlogo={wangzhan_jichuxinxi.wangzhan_tubiao_lianjie}
        caidanxiangmu={caidanxiangmu}
        xianshi={shifougengluyou}
        onChushihuaWancheng={chuli_yemian_zujian_chushihua}
      />
    </>
  );
}

function App() {
  // 记录组件开始时间
  const kaishi_shijian_ref = React.useRef(Date.now());

  // 网站基础信息状态
  const [wangzhan_jichuxinxi, shezhi_wangzhan_jichuxinxi] = React.useState({
    wangzhan_mingcheng: 'RO百科', // 默认值
    wangzhan_tubiao_lianjie: 'https://pan.new-cdn.com/f/GONUG/yunluo.jpg', // 默认值
    dingbu_daohang: [ // 默认导航菜单
      { mingcheng: '怪物数据', lianjie: '/guaiwushuju' },
      { mingcheng: '物品数据', lianjie: '/wupinshuju' },
      { mingcheng: '地图数据', lianjie: '/ditushuju' },
      { mingcheng: '技能数据', lianjie: '/jinengshuju' }
    ]
  });

  // 组件准备状态 - 动态管理不同页面的组件状态
  const [zujian_zhunbei_zhuangtai, shezhi_zujian_zhunbei_zhuangtai] = React.useState({
    zhuti_zhunbei: false,
    daohanglan_zhunbei: false,
    wasm_zhunbei: false,
    // 动态页面组件状态将根据当前页面添加
  });

  // 隐藏初始预加载动画
  React.useEffect(() => {
    const chushiYujiazai = document.getElementById('chushi-yujiazai');
    if (chushiYujiazai) {
      chushiYujiazai.style.display = 'none';
    }
    document.body.classList.add('react-loaded');
  }, []);

  // 智能预加载管理器 - 页面识别和配置
  const { dangqianlujing } = useLuyoujiance();

  // 预加载页面配置 - 只有配置的页面才会显示预加载
  const yujiazai_yemian_peizhi = React.useMemo(() => ({
    '/': {
      qiyong: true,
      xianshi_jindutiao: false,
      zuiduan_xianshi_shijian: 2000,
      donghua_leixing: 'shouye', // 首页专用动画
      xuyao_daohanglan: true // 首页需要导航栏
    },
    // 可以在这里添加其他页面的预加载配置
    // '/guaiwushuju': {
    //   qiyong: true,
    //   wenzi: '正在加载怪物数据...',
    //   xianshi_jindutiao: true,
    //   zuiduan_xianshi_shijian: 1500,
    //   donghua_leixing: 'shuju',
    //   xuyao_daohanglan: true // 数据页面也需要导航栏
    // },
    // '/nihao': {
    //   qiyong: true,
    //   wenzi: '正在加载页面...',
    //   xianshi_jindutiao: true,
    //   zuiduan_xianshi_shijian: 1000,
    //   donghua_leixing: 'moren',
    //   xuyao_daohanglan: false // 这个页面不需要导航栏
    // }
  }), []);

  // 获取当前页面的预加载配置
  const dangqian_yemian_peizhi = React.useMemo(() => {
    return yujiazai_yemian_peizhi[dangqianlujing] || { qiyong: false };
  }, [yujiazai_yemian_peizhi, dangqianlujing]);

  // 预加载状态管理 - 根据页面配置决定是否启用
  const {
    shifouyujiazai,
    tingzhiyujiazai
  } = useYujiazai({
    chushizhuangtai: dangqian_yemian_peizhi.qiyong, // 根据页面配置决定初始状态
    zuiduan_xianshi_shijian: dangqian_yemian_peizhi.zuiduan_xianshi_shijian || 1000,
    onkaishi: () => {
      console.log(`🎬 [智能预加载] ${dangqianlujing} 页面预加载开始`, {
        shijian: new Date().toISOString(),
        peizhi: dangqian_yemian_peizhi
      });
    },
    ontingzhi: () => {
      console.log(`🎬 [智能预加载] ${dangqianlujing} 页面预加载结束`, {
        shijian: new Date().toISOString()
      });
    }
  });

  // 使用useRequest管理前端初始化
  useRequest(qianduanchushihua, {
    manual: false, // 自动执行
    cacheKey: 'wasm-init', // 缓存key，防止重复初始化
    staleTime: Infinity, // 永不过期，确保只初始化一次
    onSuccess: (chushihua_jieguo) => {
      // 如果获取到了网站基础信息，更新状态
      if (chushihua_jieguo && chushihua_jieguo.shuju) {
        const { wangzhan_mingcheng, wangzhan_tubiao_lianjie, dingbu_daohang } = chushihua_jieguo.shuju;
        if (wangzhan_mingcheng || wangzhan_tubiao_lianjie || dingbu_daohang) {
          shezhi_wangzhan_jichuxinxi(prev => ({
            wangzhan_mingcheng: wangzhan_mingcheng || prev.wangzhan_mingcheng,
            wangzhan_tubiao_lianjie: wangzhan_tubiao_lianjie || prev.wangzhan_tubiao_lianjie,
            dingbu_daohang: dingbu_daohang || prev.dingbu_daohang
          }));
          console.log('🎯 [App] 更新网站基础信息状态:', {
            wangzhan_mingcheng,
            wangzhan_tubiao_lianjie,
            dingbu_daohang
          });
        }
      }

      // 标记WASM初始化完成
      shezhi_zujian_zhunbei_zhuangtai(prev => ({
        ...prev,
        wasm_zhunbei: true
      }));
    },
    onError: () => {
      // 即使失败也标记为完成，避免无限等待
      shezhi_zujian_zhunbei_zhuangtai(prev => ({
        ...prev,
        wasm_zhunbei: true
      }));
    }
  });

  // 智能页面组件状态管理 - 根据当前页面动态管理
  const [yemian_zujian_zhuangtai, shezhi_yemian_zujian_zhuangtai] = React.useState({});

  // 通用页面组件初始化处理器
  const chuli_yemian_zujian_chushihua = React.useCallback((zujianmingcheng) => {
    console.log(`🎯 [${dangqianlujing}页面] ${zujianmingcheng} 组件初始化完成`);

    shezhi_yemian_zujian_zhuangtai(prev => ({
      ...prev,
      [`${zujianmingcheng}_zhunbei`]: true
    }));
  }, [dangqianlujing]);

  // 根据当前页面获取需要等待的组件列表
  const huoqu_dangqian_yemian_zujian_liebiao = React.useCallback(() => {
    switch (dangqianlujing) {
      case '/':
        return ['lunboguanggao', 'guaiwuliebiao'];
      // 可以在这里添加其他页面的组件列表
      // case '/guaiwushuju':
      //   return ['guaiwuliebiao', 'shujutongji'];
      default:
        return [];
    }
  }, [dangqianlujing]);

  // 检查当前页面是否需要导航栏 - 优先使用页面配置，回退到路由检测
  const dangqian_yemian_xuyao_daohanglan = React.useMemo(() => {
    // 如果页面配置中明确指定了是否需要导航栏，使用配置
    if (dangqian_yemian_peizhi.xuyao_daohanglan !== undefined) {
      return dangqian_yemian_peizhi.xuyao_daohanglan;
    }

    // 否则使用现有的路由检测逻辑作为回退
    const xianshi_daohanglan_luyou = [
      '/',
      '',
      '/guaiwushuju',
      '/wupinshuju',
      '/ditushuju',
      '/jinengshuju'
    ];
    return xianshi_daohanglan_luyou.includes(dangqianlujing);
  }, [dangqianlujing, dangqian_yemian_peizhi]);

  // 路由变化时重置页面组件状态
  React.useEffect(() => {
    console.log(`🔄 [智能预加载] 路由变化到: ${dangqianlujing}`);

    // 重置页面组件状态
    shezhi_yemian_zujian_zhuangtai({});

    // 重置组件准备状态中的页面组件部分
    shezhi_zujian_zhunbei_zhuangtai(prev => ({
      ...prev,
      yemian_zujian_zhunbei: false
    }));
  }, [dangqianlujing]);

  // 监听页面组件准备状态
  React.useEffect(() => {
    const xuyao_dengdai_zujian = huoqu_dangqian_yemian_zujian_liebiao();

    if (xuyao_dengdai_zujian.length === 0) {
      // 如果当前页面不需要等待任何组件，直接标记为完成
      shezhi_zujian_zhunbei_zhuangtai(prev => ({
        ...prev,
        yemian_zujian_zhunbei: true
      }));
      return;
    }

    // 检查所有需要的组件是否都已准备好
    const suoyou_zujian_zhunbei = xuyao_dengdai_zujian.every(
      zujianming => yemian_zujian_zhuangtai[`${zujianming}_zhunbei`]
    );

    if (suoyou_zujian_zhunbei) {
      console.log(`🎯 [${dangqianlujing}页面] 所有页面组件初始化完成`);
      shezhi_zujian_zhunbei_zhuangtai(prev => ({
        ...prev,
        yemian_zujian_zhunbei: true
      }));
    }
  }, [yemian_zujian_zhuangtai, huoqu_dangqian_yemian_zujian_liebiao, dangqianlujing]);

  // 智能预加载结束逻辑 - 根据页面配置和组件状态决定
  React.useEffect(() => {
    // 如果当前页面没有启用预加载，直接返回
    if (!dangqian_yemian_peizhi.qiyong) {
      return;
    }

    const { zhuti_zhunbei, daohanglan_zhunbei, wasm_zhunbei, yemian_zujian_zhunbei } = zujian_zhunbei_zhuangtai;

    // 根据页面是否需要导航栏来决定检查条件
    let suoyou_jiben_zujian_zhunbei;
    if (dangqian_yemian_xuyao_daohanglan) {
      // 需要导航栏的页面：检查所有组件包括导航栏
      suoyou_jiben_zujian_zhunbei = zhuti_zhunbei && daohanglan_zhunbei && wasm_zhunbei && yemian_zujian_zhunbei;
      console.log(`🔍 [智能预加载] ${dangqianlujing} 页面组件状态检查 (含导航栏):`, {
        zhuti_zhunbei,
        daohanglan_zhunbei,
        wasm_zhunbei,
        yemian_zujian_zhunbei
      });
    } else {
      // 不需要导航栏的页面：跳过导航栏检查
      suoyou_jiben_zujian_zhunbei = zhuti_zhunbei && wasm_zhunbei && yemian_zujian_zhunbei;
      console.log(`🔍 [智能预加载] ${dangqianlujing} 页面组件状态检查 (无导航栏):`, {
        zhuti_zhunbei,
        wasm_zhunbei,
        yemian_zujian_zhunbei
      });
    }

    // 检查是否所有必要组件都准备好了
    if (suoyou_jiben_zujian_zhunbei) {
      console.log(`🎯 [智能预加载] ${dangqianlujing} 页面所有组件初始化完成，准备结束预加载`);

      // 计算已经过去的时间
      const yijing_guoqu_shijian = Date.now() - kaishi_shijian_ref.current;
      const zuiduan_xianshi_shijian = dangqian_yemian_peizhi.zuiduan_xianshi_shijian || 1000;

      // 如果还没到最短显示时间，等待剩余时间
      if (yijing_guoqu_shijian < zuiduan_xianshi_shijian) {
        const shengyu_shijian = zuiduan_xianshi_shijian - yijing_guoqu_shijian;
        console.log(`🎯 [智能预加载] 等待剩余时间: ${shengyu_shijian}ms`);

        setTimeout(() => {
          tingzhiyujiazai();
        }, shengyu_shijian);
      } else {
        // 已经超过最短显示时间，立即停止
        console.log(`🎯 [智能预加载] 立即结束 ${dangqianlujing} 页面预加载`);
        tingzhiyujiazai();
      }
    }
  }, [zujian_zhunbei_zhuangtai, tingzhiyujiazai, dangqian_yemian_peizhi, dangqianlujing, dangqian_yemian_xuyao_daohanglan]);

  // 导航栏配置（从网站基础信息中获取，首页链接已内置在组件中）
  const caidanxiangmu = wangzhan_jichuxinxi.dingbu_daohang.map(xiangmu => ({
    ...xiangmu,
    huoyue: false // 活跃状态由组件内部处理
  }));
  return (
    <Minganbuju
      qiyongquanjuyangshi={true}
    >
      {/* 智能预加载动画组件 - 根据页面配置显示定制化内容 */}
      {dangqian_yemian_peizhi.qiyong && (
        <Yujiazaizujian
          xianshi={shifouyujiazai}
          wenzi={dangqian_yemian_peizhi.wenzi || ''}
          xianshi_jindutiao={dangqian_yemian_peizhi.xianshi_jindutiao || false}
          donghua_leixing={dangqian_yemian_peizhi.donghua_leixing}
        />
      )}

      <ZhutiheDaohanglanJianceqi
        onZhutiZhunbei={() => {
          shezhi_zujian_zhunbei_zhuangtai(prev => ({
            ...prev,
            zhuti_zhunbei: true
          }));
        }}
        onDaohanglanZhunbei={() => {
          shezhi_zujian_zhunbei_zhuangtai(prev => ({
            ...prev,
            daohanglan_zhunbei: true
          }));
        }}
        caidanxiangmu={caidanxiangmu}
        wangzhan_jichuxinxi={wangzhan_jichuxinxi}
        chuli_yemian_zujian_chushihua={chuli_yemian_zujian_chushihua}
      />

      <AppNeirong onZujianChushihuaWancheng={chuli_yemian_zujian_chushihua} />
    </Minganbuju>
  );
}

// 应用内容组件（使用主题）
function AppNeirong({ onZujianChushihuaWancheng }) {
  return (
    <div style={{
      minHeight: '100vh',
      /* 移动端优化 */
      touchAction: 'manipulation',
      WebkitTouchCallout: 'none',
      WebkitUserSelect: 'none',
      userSelect: 'none',
      /* 隐藏滚动条但保持滚动功能 */
      overflowX: 'hidden',
      scrollbarWidth: 'none',
      msOverflowStyle: 'none'
    }}>
      {/* 首页组件 - 由页面组件自己处理与导航栏的间隔 */}
      <Shouye onZujianChushihuaWancheng={onZujianChushihuaWancheng} />
    </div>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
