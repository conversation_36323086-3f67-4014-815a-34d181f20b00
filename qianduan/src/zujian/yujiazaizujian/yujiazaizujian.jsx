import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../minganbuju/zhutitiqigong.js';
import { useEffect, useRef, useMemo, memo } from 'react';

// 全屏遮罩容器 - 主题适配凄凉风格
const Quan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: ${props => props.$isanhei
    ? `linear-gradient(135deg,
        #0a0a0f 0%,
        #1a1a2e 30%,
        #16213e 60%,
        #0f0f23 100%
      )`
    : `linear-gradient(135deg,
        #f5f3ff 0%,
        #ede9fe 30%,
        #ddd6fe 60%,
        #f0f4f8 100%
      )`
  };
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(15px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.$isanhei
      ? `radial-gradient(ellipse at 30% 20%,
          rgba(70, 130, 180, 0.1) 0%,
          transparent 50%
        ),
        radial-gradient(ellipse at 70% 80%,
          rgba(100, 149, 237, 0.08) 0%,
          transparent 50%
        )`
      : `radial-gradient(ellipse at 30% 20%,
          rgba(139, 92, 246, 0.15) 0%,
          transparent 50%
        ),
        radial-gradient(ellipse at 70% 80%,
          rgba(167, 139, 250, 0.12) 0%,
          transparent 50%
        )`
    };
    pointer-events: none;
    animation: qiliang_bianhua 8s ease-in-out infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.$isanhei
      ? `radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.08), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.04), transparent)`
      : `radial-gradient(2px 2px at 20px 30px, rgba(139,92,246,0.25), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(167,139,250,0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(196,181,253,0.15), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(221,214,254,0.1), transparent)`
    };
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: xingchen_piaodong 20s linear infinite;
    pointer-events: none;
    opacity: 0.3;
  }

  @keyframes qiliang_bianhua {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.4; }
  }

  @keyframes xingchen_piaodong {
    0% { transform: translate(0, 0); }
    100% { transform: translate(-200px, -200px); }
  }
`;

// 加载器容器
const Jiazaiqi_rongqi = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.jianju.da};
`;

// 旋转加载器 - 主题适配凄凉风格
const Xuanzhuanjiazaiqi = styled(motion.div)`
  width: 140px;
  height: 140px;
  border: 4px solid transparent;
  border-top: 4px solid ${props => props.$isanhei
    ? 'rgba(135, 206, 235, 0.8)'
    : 'rgba(139, 92, 246, 0.8)'
  };
  border-right: 4px solid ${props => props.$isanhei
    ? 'rgba(135, 206, 235, 0.4)'
    : 'rgba(139, 92, 246, 0.4)'
  };
  border-bottom: 4px solid ${props => props.$isanhei
    ? 'rgba(135, 206, 235, 0.2)'
    : 'rgba(139, 92, 246, 0.2)'
  };
  border-left: 4px solid ${props => props.$isanhei
    ? 'rgba(135, 206, 235, 0.1)'
    : 'rgba(139, 92, 246, 0.1)'
  };
  border-radius: 50%;
  position: relative;
  box-shadow: ${props => props.$isanhei
    ? `0 0 40px rgba(135, 206, 235, 0.3),
       0 0 80px rgba(135, 206, 235, 0.1),
       inset 0 0 30px rgba(135, 206, 235, 0.1)`
    : `0 0 40px rgba(139, 92, 246, 0.3),
       0 0 80px rgba(139, 92, 246, 0.1),
       inset 0 0 30px rgba(139, 92, 246, 0.1)`
  };
  backdrop-filter: blur(5px);

  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border: 1px solid ${props => props.$isanhei
      ? 'rgba(135, 206, 235, 0.2)'
      : 'rgba(139, 92, 246, 0.2)'
    };
    border-radius: 50%;
    animation: qiliang_bohuan 4s ease-in-out infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: -16px;
    left: -16px;
    right: -16px;
    bottom: -16px;
    border: 1px solid ${props => props.$isanhei
      ? 'rgba(135, 206, 235, 0.1)'
      : 'rgba(139, 92, 246, 0.1)'
    };
    border-radius: 50%;
    animation: qiliang_bohuan 6s ease-in-out infinite reverse;
  }

  @keyframes qiliang_bohuan {
    0%, 100% {
      transform: scale(1);
      opacity: 0.2;
      filter: blur(0px);
    }
    50% {
      transform: scale(1.2);
      opacity: 0.6;
      filter: blur(1px);
    }
  }

  @media (max-width: 768px) {
    width: 110px;
    height: 110px;
    border-width: 3px;
  }

  @media (max-width: 480px) {
    width: 90px;
    height: 90px;
    border-width: 2px;
  }
`;

// 中心波纹扩散效果 - 主题适配凄凉风格
const Zhongxinbowen = styled(motion.div)`
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  transform: translate(-50%, -50%);

  // 中心圆点
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: ${props => props.$isanhei
      ? `radial-gradient(circle,
          rgba(255, 255, 255, 0.9) 0%,
          rgba(176, 196, 222, 0.8) 40%,
          rgba(135, 206, 235, 0.6) 80%,
          transparent 100%
        )`
      : `radial-gradient(circle,
          rgba(255, 255, 255, 0.95) 0%,
          rgba(196, 181, 253, 0.8) 40%,
          rgba(139, 92, 246, 0.6) 80%,
          transparent 100%
        )`
    };
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: ${props => props.$isanhei
      ? `0 0 15px rgba(135, 206, 235, 0.6),
         0 0 30px rgba(135, 206, 235, 0.3)`
      : `0 0 15px rgba(139, 92, 246, 0.6),
         0 0 30px rgba(139, 92, 246, 0.3)`
    };
    animation: zhongxin_maibo 2s ease-in-out infinite;
  }

  @keyframes zhongxin_maibo {
    0%, 100% {
      opacity: 0.8;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.3);
    }
  }

  @media (max-width: 768px) {
    width: 16px;
    height: 16px;

    &::before {
      width: 6px;
      height: 6px;
    }
  }

  @media (max-width: 480px) {
    width: 12px;
    height: 12px;

    &::before {
      width: 4px;
      height: 4px;
    }
  }
`;

// 波纹圆环组件
const Bowenyuanhuan = styled(motion.div)`
  position: absolute;
  top: 50%;
  left: 50%;
  border: 2px solid ${props => props.$isanhei
    ? 'rgba(135, 206, 235, 0.4)'
    : 'rgba(139, 92, 246, 0.4)'
  };
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;

  @media (max-width: 768px) {
    border-width: 1.5px;
  }

  @media (max-width: 480px) {
    border-width: 1px;
  }
`;

// 加载文字
const Jiazaiwenzi = styled(motion.p)`
  font-size: ${props => props.theme.ziti.daxiao.da};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin: 0;
  text-align: center;
  letter-spacing: 2px;

  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
    letter-spacing: 1px;
  }

  @media (max-width: 480px) {
    font-size: ${props => props.theme.ziti.daxiao.xiao};
    letter-spacing: 0.5px;
  }
`;

// 进度条容器
const Jindutiao_rongqi = styled(motion.div)`
  width: 200px;
  height: 4px;
  background: ${props => props.theme.yanse.biankuang};
  border-radius: ${props => props.theme.yuanjiao.xiao};
  overflow: hidden;
  position: relative;

  @media (max-width: 768px) {
    width: 160px;
    height: 3px;
  }

  @media (max-width: 480px) {
    width: 120px;
    height: 2px;
  }
`;

// 进度条填充
const Jindutiao_tianchong = styled(motion.div)`
  height: 100%;
  background: linear-gradient(90deg, 
    ${props => props.$isanhei 
      ? props.theme.yanse.danjinse 
      : props.theme.yanse.danlanse
    } 0%,
    ${props => props.$isanhei 
      ? props.theme.yanse.danjinse_hou 
      : props.theme.yanse.danlanse_hou
    } 50%,
    ${props => props.$isanhei 
      ? props.theme.yanse.danjinse 
      : props.theme.yanse.danlanse
    } 100%
  );
  border-radius: ${props => props.theme.yuanjiao.xiao};
`;

// 装饰性粒子效果 - 主题适配凄凉风格
const Zhuangshilizi = styled(motion.div)`
  position: absolute;
  width: 8px;
  height: 8px;
  background: ${props => props.$isanhei
    ? `radial-gradient(circle,
        rgba(176, 196, 222, 0.8) 0%,
        rgba(135, 206, 235, 0.6) 30%,
        rgba(70, 130, 180, 0.4) 60%,
        transparent 100%
      )`
    : `radial-gradient(circle,
        rgba(196, 181, 253, 0.8) 0%,
        rgba(167, 139, 250, 0.6) 30%,
        rgba(139, 92, 246, 0.4) 60%,
        transparent 100%
      )`
  };
  border-radius: 50%;
  pointer-events: none;
  box-shadow: ${props => props.$isanhei
    ? `0 0 12px rgba(135, 206, 235, 0.6),
       0 0 24px rgba(135, 206, 235, 0.3),
       0 0 36px rgba(135, 206, 235, 0.1)`
    : `0 0 12px rgba(139, 92, 246, 0.6),
       0 0 24px rgba(139, 92, 246, 0.3),
       0 0 36px rgba(139, 92, 246, 0.1)`
  };
  filter: blur(0.5px);

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 2px;
    background: ${props => props.$isanhei
      ? 'rgba(255, 255, 255, 0.9)'
      : 'rgba(255, 255, 255, 1)'
    };
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: ${props => props.$isanhei
      ? '0 0 4px rgba(255, 255, 255, 0.7)'
      : '0 0 4px rgba(255, 255, 255, 0.9)'
    };
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    border: 1px solid ${props => props.$isanhei
      ? 'rgba(135, 206, 235, 0.2)'
      : 'rgba(139, 92, 246, 0.2)'
    };
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: lizi_bohuan 4s ease-in-out infinite;
  }

  @keyframes lizi_bohuan {
    0%, 100% {
      transform: translate(-50%, -50%) scale(0.5);
      opacity: 0.2;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.5);
      opacity: 0.6;
    }
  }

  @media (max-width: 768px) {
    width: 6px;
    height: 6px;

    &::after {
      width: 12px;
      height: 12px;
    }
  }

  @media (max-width: 480px) {
    width: 5px;
    height: 5px;

    &::after {
      width: 10px;
      height: 10px;
    }
  }
`;

/**
 * 智能预加载动画组件
 * @param {boolean} xianshi - 是否显示预加载动画
 * @param {string} wenzi - 加载提示文字
 * @param {boolean} xianshi_jindutiao - 是否显示进度条
 * @param {string} donghua_leixing - 动画类型，用于定制化不同页面的动画效果
 */
const Yujiazaizujian = memo(({
  xianshi = false,
  wenzi = '加载中...',
  xianshi_jindutiao = true,
  donghua_leixing = 'moren'
}) => {
  const qianyi_zhuti_ref = useRef(null);

  const { dangqianzhuti } = useShiyongzhuti();

  // 使用 useMemo 稳定主题计算，避免重新渲染时的闪烁
  const isanhei = useMemo(() => {
    return dangqianzhuti === 'anhei';
  }, [dangqianzhuti]);

  // 记录主题状态变化
  useEffect(() => {
    qianyi_zhuti_ref.current = dangqianzhuti;
  }, [dangqianzhuti]);

  // 使用 useMemo 稳定动画配置，避免重新渲染时重新创建
  const zhezhaoceng_donghua = useMemo(() => ({
    initial: { opacity: 1 }, // 初始就是可见的
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.3, ease: "easeInOut" }
  }), []);

  const jiazaiqi_donghua = useMemo(() => ({
    initial: { scale: 1, opacity: 1 }, // 初始就是可见的
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.8, opacity: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  }), []);

  // 使用 useMemo 稳定旋转动画配置
  const xuanzhuan_donghua = useMemo(() => ({
    animate: {
      rotate: 360,
      scale: [1, 1.02, 1],
      filter: ["blur(0px)", "blur(0.5px)", "blur(0px)"]
    },
    transition: {
      rotate: { duration: 4, repeat: Infinity, ease: "linear" },
      scale: { duration: 6, repeat: Infinity, ease: "easeInOut" },
      filter: { duration: 5, repeat: Infinity, ease: "easeInOut" }
    }
  }), []);

  // 使用 useMemo 稳定波纹扩散动画配置
  const bowen_donghua = useMemo(() => ({
    animate: {
      scale: [1, 1.1, 1],
      opacity: [0.6, 0.9, 0.6]
    },
    transition: {
      scale: { duration: 3, repeat: Infinity, ease: "easeInOut" },
      opacity: { duration: 3, repeat: Infinity, ease: "easeInOut" }
    }
  }), []);

  // 使用 useMemo 稳定波纹圆环生成，避免重新渲染时重新创建
  const bowenyuanhuan_shuzu = useMemo(() => {
    const yuanhuan_shuzu = [];
    for (let i = 0; i < 4; i++) {
      const chushi_daxiao = 30 + i * 25; // 初始大小递增
      const zuida_daxiao = 120 + i * 40; // 最大大小递增
      const yanchi = i * 0.8; // 延迟时间递增

      yuanhuan_shuzu.push(
        <Bowenyuanhuan
          key={i}
          $isanhei={isanhei}
          animate={{
            width: [chushi_daxiao, zuida_daxiao, chushi_daxiao],
            height: [chushi_daxiao, zuida_daxiao, chushi_daxiao],
            opacity: [0, 0.6, 0],
            borderWidth: [2, 1, 0]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: yanchi,
            ease: "easeOut",
            times: [0, 0.4, 1]
          }}
        />
      );
    }
    return yuanhuan_shuzu;
  }, [isanhei]);

  // 文字淡入淡出动画
  const wenzi_donghua = {
    initial: { opacity: 1, y: 0 }, // 初始就是可见的
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: "easeOut" }
  };

  // 进度条动画
  const jindutiao_donghua = {
    initial: { width: "0%" },
    animate: { width: "100%" },
    transition: { duration: 2, repeat: Infinity, ease: "easeInOut" }
  };

  // 使用 useMemo 稳定装饰粒子生成
  const zhuangshilizi_shuzu = useMemo(() => {
    const lizi_shuzu = [];
    for (let i = 0; i < 16; i++) {
      const jiaodu = (i * 22.5) * (Math.PI / 180);
      const banjing = 180 + (i % 3) * 30; // 不同层次的距离
      const x = Math.cos(jiaodu) * banjing;
      const y = Math.sin(jiaodu) * banjing;

      lizi_shuzu.push(
        <Zhuangshilizi
          key={i}
          $isanhei={isanhei}
          style={{ left: `calc(50% + ${x}px)`, top: `calc(50% + ${y}px)` }}
          animate={{
            scale: [0.2, 0.8, 0.2],
            opacity: [0.1, 0.7, 0.1],
            rotate: [0, 180, 360],
            x: [0, Math.cos(jiaodu + Math.PI/6) * 15, 0],
            y: [0, Math.sin(jiaodu + Math.PI/6) * 15, 0],
            filter: ["blur(0px)", "blur(1px)", "blur(0px)"]
          }}
          transition={{
            duration: 5 + (i % 4) * 1.5,
            repeat: Infinity,
            delay: i * 0.3,
            ease: "easeInOut",
            times: [0, 0.3, 0.7, 1]
          }}
        />
      );
    }
    return lizi_shuzu;
  }, [isanhei]);

  // 根据动画类型和主题设置CSS变量
  const cssbianliang = useMemo(() => {
    // 基础样式
    let beijing, biankuang, guangyun;

    // 根据动画类型定制化样式
    switch (donghua_leixing) {
      case 'shouye':
        // 首页专用：更温暖的色调
        beijing = isanhei
          ? 'linear-gradient(135deg, #0f0a1a 0%, #1a1a3e 30%, #2e1a3e 60%, #1a0f2a 100%)'
          : 'linear-gradient(135deg, #fef7ff 0%, #f3e8ff 30%, #e9d5ff 60%, #f8fafc 100%)';
        biankuang = isanhei ? 'rgba(168, 85, 247, 0.8)' : 'rgba(147, 51, 234, 0.8)';
        guangyun = isanhei
          ? '0 0 40px rgba(168, 85, 247, 0.4), 0 0 80px rgba(168, 85, 247, 0.2)'
          : '0 0 40px rgba(147, 51, 234, 0.4), 0 0 80px rgba(147, 51, 234, 0.2)';
        break;
      case 'shuju':
        // 数据页面：科技感蓝色
        beijing = isanhei
          ? 'linear-gradient(135deg, #0a0f1a 0%, #1a2e3e 30%, #1a3e4e 60%, #0f1a2a 100%)'
          : 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 30%, #bae6fd 60%, #f8fafc 100%)';
        biankuang = isanhei ? 'rgba(56, 189, 248, 0.8)' : 'rgba(14, 165, 233, 0.8)';
        guangyun = isanhei
          ? '0 0 40px rgba(56, 189, 248, 0.4), 0 0 80px rgba(56, 189, 248, 0.2)'
          : '0 0 40px rgba(14, 165, 233, 0.4), 0 0 80px rgba(14, 165, 233, 0.2)';
        break;
      default:
        // 默认样式
        beijing = isanhei
          ? 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 30%, #16213e 60%, #0f0f23 100%)'
          : 'linear-gradient(135deg, #f5f3ff 0%, #ede9fe 30%, #ddd6fe 60%, #f0f4f8 100%)';
        biankuang = isanhei ? 'rgba(135, 206, 235, 0.8)' : 'rgba(139, 92, 246, 0.8)';
        guangyun = isanhei
          ? '0 0 40px rgba(135, 206, 235, 0.3), 0 0 80px rgba(135, 206, 235, 0.1)'
          : '0 0 40px rgba(139, 92, 246, 0.3), 0 0 80px rgba(139, 92, 246, 0.1)';
    }

    return {
      '--yujiazai-beijing': beijing,
      '--yujiazai-biankuang': biankuang,
      '--yujiazai-guangyun': guangyun
    };
  }, [isanhei, donghua_leixing]);

  return (
    <AnimatePresence mode="wait">
      {xianshi && (
        <Quanpingzhezhaoceng
          $isanhei={isanhei}
          style={cssbianliang}
          {...zhezhaoceng_donghua}
        >
          {/* 装饰粒子 */}
          {zhuangshilizi_shuzu}
          
          <Jiazaiqi_rongqi {...jiazaiqi_donghua}>
            {/* 主要加载器 */}
            <Xuanzhuanjiazaiqi
              $isanhei={isanhei}
              {...xuanzhuan_donghua}
            >
              {/* 波纹圆环效果 */}
              {bowenyuanhuan_shuzu}

              {/* 中心波纹点 */}
              <Zhongxinbowen
                $isanhei={isanhei}
                {...bowen_donghua}
              />
            </Xuanzhuanjiazaiqi>

            {/* 加载文字 */}
            <Jiazaiwenzi {...wenzi_donghua}>
              {wenzi}
            </Jiazaiwenzi>

            {/* 进度条 */}
            {xianshi_jindutiao && (
              <Jindutiao_rongqi
                initial={{ opacity: 1, scale: 1 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.3 }}
              >
                <Jindutiao_tianchong
                  $isanhei={isanhei}
                  {...jindutiao_donghua}
                />
              </Jindutiao_rongqi>
            )}
          </Jiazaiqi_rongqi>
        </Quanpingzhezhaoceng>
      )}
    </AnimatePresence>
  );
});

export default Yujiazaizujian;
