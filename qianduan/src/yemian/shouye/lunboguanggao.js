import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>biaomian } from '../../zujian/minganbuju/yangshihuazujian.js';
import { meiti_chaxun } from '../../gongju/shebeishiPei_gongju.js';
import { wangguanpeizhi } from '../../peizhi/wangguanpeizhi.js';

// 轮播容器
const Lunborongqi = styled.div`
  position: relative;
  width: 480px;
  height: 270px;
  cursor: pointer;
  user-select: none;
  overflow: hidden;
  border-radius: 15px;

  ${meiti_chaxun.shouji} {
    width: 100%;
    max-width: none;
    height: 180px;
    border-radius: 12px;
  }

  ${meiti_chaxun.pingban} {
    width: 100%;
    max-width: none;
    height: 220px;
    border-radius: 12px;
  }
`;

// 广告图片
const Guanggaotupian = styled(motion.img)`
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 15px;

  ${meiti_chaxun.shouji} {
    border-radius: 12px;
  }

  ${meiti_chaxun.pingban} {
    border-radius: 12px;
  }
`;

// 指示器容器
const Zhishiqirongqi = styled.div`
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 6px;
  z-index: 20;
`;

// 指示器点
const Zhishiqidian = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'huoyue'
})`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.huoyue
    ? '#ffffff'
    : 'rgba(255, 255, 255, 0.5)'};
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);

  &:hover {
    background: #ffffff;
    transform: scale(1.3);
  }
`;



// 轮播广告组件
function Lunboguanggao({ onChushihuaWancheng }) {
  const [dangqiansuoyin, shedangqiansuoyin] = useState(0);
  const [zidongbofang, shezidongbofang] = useState(true);
  const [shoujiduanhuadong, sheshoujiduanhuadong] = useState(false);
  const [tupianjiazaizhuangtai, shezhi_tupianjiazaizhuangtai] = useState({});
  const [chushihuawancheng, shezhi_chushihuawancheng] = useState(false);
  const lunborongqiref = useRef(null);
  
  // 使用配置文件中的网关地址
  
  // 广告图片数组
  const guanggaotupian = Array.from({ length: 6 }, (_, index) => ({
    id: index + 1,
    url: wangguanpeizhi.huoquziyuandizhi(`/guanggao/guanggao${index + 1}.png`),
    alt: `广告 ${index + 1}`
  }));

  // 处理图片加载完成
  const chuli_tupian_jiazaiwancheng = (tupianid) => {
    shezhi_tupianjiazaizhuangtai(prev => ({
      ...prev,
      [tupianid]: true
    }));
  };

  // 处理图片加载失败
  const chuli_tupian_jiazaishibai = (tupianid, error) => {
    console.warn(`广告图片加载失败: ${error.target.src}`);
    shezhi_tupianjiazaizhuangtai(prev => ({
      ...prev,
      [tupianid]: false
    }));
  };
  
  // 检查初始化完成状态
  useEffect(() => {
    const yijiazai_shuliang = Object.values(tupianjiazaizhuangtai).filter(Boolean).length;
    const shibai_shuliang = Object.values(tupianjiazaizhuangtai).filter(status => status === false).length;
    const zongshu = guanggaotupian.length;

    // 当至少有一张图片加载成功，或者所有图片都尝试加载完成时，认为初始化完成
    if ((yijiazai_shuliang > 0 || shibai_shuliang === zongshu) && !chushihuawancheng) {
      shezhi_chushihuawancheng(true);

      // 延迟通知父组件，确保DOM渲染完成
      setTimeout(() => {
        if (onChushihuaWancheng) {
          onChushihuaWancheng('lunboguanggao');
        }
      }, 100);
    }
  }, [tupianjiazaizhuangtai, guanggaotupian.length, chushihuawancheng, onChushihuaWancheng]);

  // 自动轮播
  useEffect(() => {
    if (!zidongbofang) return;

    const jiangeqi = setInterval(() => {
      shedangqiansuoyin(prev => (prev + 1) % guanggaotupian.length);
    }, 3000); // 3秒切换一次

    return () => clearInterval(jiangeqi);
  }, [zidongbofang, guanggaotupian.length]);
  
  // 切换到指定索引
  const qiehuandao = (suoyin) => {
    shedangqiansuoyin(suoyin);
    // 手动切换后暂停自动播放3秒
    shezidongbofang(false);
    setTimeout(() => {
      shezidongbofang(true);
    }, 3000);
  };
  
  // 鼠标悬停暂停自动播放
  const chulishubiaoruyu = () => {
    shezidongbofang(false);
  };
  
  // 鼠标离开恢复自动播放
  const chulishubiaolikai = () => {
    shezidongbofang(true);
  };

  // 处理点击跳转
  const chulidianjitiaozhuang = () => {
    if (!shoujiduanhuadong) {
      window.open('https://luoluo.blyfw.cn', '_blank');
    }
  };

  // 处理手势滑动结束
  const chulihuadongjieshu = (event, info) => {
    const { offset, velocity } = info;
    const huadongminjueli = 50; // 最小滑动距离
    const suduyu = 500; // 速度阈值

    if (Math.abs(offset.x) > huadongminjueli || Math.abs(velocity.x) > suduyu) {
      if (offset.x > 0) {
        // 向右滑动，显示上一张
        shedangqiansuoyin(prev =>
          prev === 0 ? guanggaotupian.length - 1 : prev - 1
        );
      } else {
        // 向左滑动，显示下一张
        shedangqiansuoyin(prev => (prev + 1) % guanggaotupian.length);
      }

      // 滑动后暂停自动播放3秒
      shezidongbofang(false);
      setTimeout(() => {
        shezidongbofang(true);
      }, 3000);
    }

    // 延迟重置滑动状态
    setTimeout(() => {
      sheshoujiduanhuadong(false);
    }, 100);
  };

  // 处理滑动开始
  const chulihuadongkaishi = () => {
    sheshoujiduanhuadong(true);
  };

  // 处理指示器点击（阻止事件冒泡）
  const chulizhishiqidianjishijian = (event, suoyin) => {
    event.stopPropagation(); // 阻止事件冒泡到父容器
    qiehuandao(suoyin);
  };
  
  // 动画配置
  const donghuapeizhi = {
    initial: { opacity: 0, scale: 1.1 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
    transition: { duration: 0.5, ease: "easeInOut" }
  };
  
  return (
    <Lunborongqi
      ref={lunborongqiref}
      onMouseEnter={chulishubiaoruyu}
      onMouseLeave={chulishubiaolikai}
      onClick={chulidianjitiaozhuang}
    >
      {/* 广告图片 */}
      <AnimatePresence mode="wait">
        <Guanggaotupian
          key={dangqiansuoyin}
          src={guanggaotupian[dangqiansuoyin].url}
          alt={guanggaotupian[dangqiansuoyin].alt}
          variants={donghuapeizhi}
          initial="initial"
          animate="animate"
          exit="exit"
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          dragElastic={0.2}
          onDragStart={chulihuadongkaishi}
          onDragEnd={chulihuadongjieshu}
          onLoad={() => chuli_tupian_jiazaiwancheng(guanggaotupian[dangqiansuoyin].id)}
          onError={(e) => chuli_tupian_jiazaishibai(guanggaotupian[dangqiansuoyin].id, e)}
        />
      </AnimatePresence>
      

      
      {/* 指示器 */}
      <Zhishiqirongqi>
        {guanggaotupian.map((_, suoyin) => (
          <Zhishiqidian
            key={suoyin}
            huoyue={suoyin === dangqiansuoyin}
            onClick={(event) => chulizhishiqidianjishijian(event, suoyin)}
          />
        ))}
      </Zhishiqirongqi>
    </Lunborongqi>
  );
}

export default Lunboguanggao;
